# 博客系统API文档

这是一个简单的博客系统API文档，提供文章管理、评论管理等功能。

## 文章管理

### GET /api/articles

获取文章列表

**查询参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| page | integer | 否 | 页码，默认为1 |
| limit | integer | 否 | 每页数量，默认为10 |
| category | string | 否 | 文章分类 |
| status | string | 否 | 文章状态：published, draft |

**响应示例:**

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "如何学习JavaScript",
        "summary": "JavaScript学习指南",
        "author": "张三",
        "category": "编程",
        "status": "published",
        "created_at": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 50,
      "pages": 5
    }
  }
}
```

### POST /api/articles

创建新文章

**请求头:**
- `Authorization: Bearer <token>` (必需)
- `Content-Type: application/json`

**请求体参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| title | string | 是 | 文章标题 |
| content | string | 是 | 文章内容 |
| summary | string | 否 | 文章摘要 |
| category | string | 是 | 文章分类 |
| tags | array | 否 | 文章标签 |
| status | string | 否 | 发布状态，默认为draft |

**请求示例:**

```json
{
  "title": "Vue.js 3.0 新特性介绍",
  "content": "Vue.js 3.0 带来了许多令人兴奋的新特性...",
  "summary": "介绍Vue.js 3.0的主要新特性",
  "category": "前端开发",
  "tags": ["Vue.js", "JavaScript", "前端"],
  "status": "published"
}
```

**响应示例:**

```json
{
  "code": 201,
  "message": "文章创建成功",
  "data": {
    "id": 2,
    "title": "Vue.js 3.0 新特性介绍",
    "content": "Vue.js 3.0 带来了许多令人兴奋的新特性...",
    "summary": "介绍Vue.js 3.0的主要新特性",
    "author": "李四",
    "category": "前端开发",
    "tags": ["Vue.js", "JavaScript", "前端"],
    "status": "published",
    "created_at": "2024-01-15T14:30:00Z"
  }
}
```

### GET /api/articles/{id}

获取文章详情

**路径参数:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| id | integer | 文章ID |

**响应示例:**

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "如何学习JavaScript",
    "content": "JavaScript是一门强大的编程语言...",
    "summary": "JavaScript学习指南",
    "author": "张三",
    "category": "编程",
    "tags": ["JavaScript", "编程", "学习"],
    "status": "published",
    "views": 1250,
    "likes": 89,
    "created_at": "2024-01-15T10:00:00Z",
    "updated_at": "2024-01-15T10:00:00Z"
  }
}
```

## 评论管理

### GET /api/articles/{article_id}/comments

获取文章评论列表

**路径参数:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| article_id | integer | 文章ID |

**查询参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| page | integer | 否 | 页码，默认为1 |
| limit | integer | 否 | 每页数量，默认为20 |

### POST /api/articles/{article_id}/comments

添加评论

**请求头:**
- `Authorization: Bearer <token>` (必需)

**路径参数:**

| 参数名 | 类型 | 描述 |
|--------|------|------|
| article_id | integer | 文章ID |

**请求体参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| content | string | 是 | 评论内容 |
| parent_id | integer | 否 | 父评论ID（回复评论时使用） |

**请求示例:**

```json
{
  "content": "这篇文章写得很好，学到了很多！",
  "parent_id": null
}
```

## 用户认证

### POST /api/auth/login

用户登录

**请求体参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码 |

**响应示例:**

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": 1,
      "name": "张三",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar/1.jpg"
    }
  }
}
```

### POST /api/auth/register

用户注册

**请求体参数:**

| 参数名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| name | string | 是 | 用户名 |
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码，至少8个字符 |
| password_confirmation | string | 是 | 确认密码 |

## 错误处理

API使用标准的HTTP状态码来表示请求的结果：

- `200` - 请求成功
- `201` - 资源创建成功
- `400` - 请求参数错误
- `401` - 未授权，需要登录
- `403` - 禁止访问，权限不足
- `404` - 资源不存在
- `422` - 请求参数验证失败
- `500` - 服务器内部错误

**错误响应格式:**

```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": {
    "title": ["文章标题不能为空"],
    "content": ["文章内容不能为空"]
  }
}
```

## 速率限制

API实施速率限制以防止滥用：

- **未认证用户**: 每小时100次请求
- **认证用户**: 每小时1000次请求
- **VIP用户**: 每小时5000次请求

当达到速率限制时，API将返回 `429 Too Many Requests` 状态码。

响应头中包含速率限制信息：

- `X-RateLimit-Limit`: 限制次数
- `X-RateLimit-Remaining`: 剩余次数
- `X-RateLimit-Reset`: 重置时间戳
