{"title": "用户管理系统API", "subtitle": "用户管理相关接口", "description": "提供用户注册、登录、信息管理等功能的REST API接口", "sections": [{"title": "POST /api/users/register", "subtitle": "用户注册", "description": "创建新用户账户", "content": "<h3>请求体参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead><tbody><tr><td>username</td><td><span class=\"type\">string</span></td><td>是</td><td>用户名，3-20个字符</td></tr><tr><td>email</td><td><span class=\"type\">string</span></td><td>是</td><td>邮箱地址</td></tr><tr><td>password</td><td><span class=\"type\">string</span></td><td>是</td><td>密码，至少8个字符</td></tr><tr><td>phone</td><td><span class=\"type\">string</span></td><td>否</td><td>手机号码</td></tr></tbody></table><h3>响应示例</h3><div class=\"code-example\"><pre><code>{\n  \"code\": 200,\n  \"message\": \"注册成功\",\n  \"data\": {\n    \"user_id\": 12345,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"created_at\": \"2024-01-15T10:30:00Z\"\n  }\n}</code></pre></div>"}, {"title": "POST /api/users/login", "subtitle": "用户登录", "description": "用户身份验证和获取访问令牌", "content": "<h3>请求体参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead><tbody><tr><td>username</td><td><span class=\"type\">string</span></td><td>是</td><td>用户名或邮箱</td></tr><tr><td>password</td><td><span class=\"type\">string</span></td><td>是</td><td>用户密码</td></tr><tr><td>remember_me</td><td><span class=\"type\">boolean</span></td><td>否</td><td>是否记住登录状态</td></tr></tbody></table><h3>响应示例</h3><div class=\"code-example\"><pre><code>{\n  \"code\": 200,\n  \"message\": \"登录成功\",\n  \"data\": {\n    \"access_token\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...\",\n    \"token_type\": \"Bearer\",\n    \"expires_in\": 3600,\n    \"user\": {\n      \"id\": 12345,\n      \"username\": \"testuser\",\n      \"email\": \"<EMAIL>\"\n    }\n  }\n}</code></pre></div>"}, {"title": "GET /api/users/{id}", "subtitle": "获取用户信息", "description": "根据用户ID获取用户详细信息", "content": "<h3>路径参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead><tbody><tr><td>id</td><td><span class=\"type\">integer</span></td><td>用户ID</td></tr></tbody></table><h3>请求头</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead><tbody><tr><td>Authorization</td><td><span class=\"type\">string</span></td><td>是</td><td>Bearer token</td></tr></tbody></table><h3>响应示例</h3><div class=\"code-example\"><pre><code>{\n  \"code\": 200,\n  \"message\": \"获取成功\",\n  \"data\": {\n    \"id\": 12345,\n    \"username\": \"testuser\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+86 138****8888\",\n    \"avatar\": \"https://example.com/avatar/12345.jpg\",\n    \"created_at\": \"2024-01-15T10:30:00Z\",\n    \"updated_at\": \"2024-01-20T15:45:00Z\",\n    \"status\": \"active\"\n  }\n}</code></pre></div>"}, {"title": "PUT /api/users/{id}", "subtitle": "更新用户信息", "description": "更新指定用户的信息", "content": "<h3>路径参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead><tbody><tr><td>id</td><td><span class=\"type\">integer</span></td><td>用户ID</td></tr></tbody></table><h3>请求体参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead><tbody><tr><td>email</td><td><span class=\"type\">string</span></td><td>否</td><td>新的邮箱地址</td></tr><tr><td>phone</td><td><span class=\"type\">string</span></td><td>否</td><td>新的手机号码</td></tr><tr><td>avatar</td><td><span class=\"type\">string</span></td><td>否</td><td>头像URL</td></tr></tbody></table><div class=\"warning-box\"><strong>⚠️ 注意:</strong> 只能更新自己的用户信息，或者需要管理员权限</div>"}, {"title": "DELETE /api/users/{id}", "subtitle": "删除用户", "description": "删除指定的用户账户", "content": "<h3>路径参数</h3><table class=\"params-table\"><thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead><tbody><tr><td>id</td><td><span class=\"type\">integer</span></td><td>用户ID</td></tr></tbody></table><div class=\"warning-box\"><strong>🔒 权限要求:</strong> 需要管理员权限才能删除用户</div><div class=\"warning-box\"><strong>⚠️ 警告:</strong> 删除用户是不可逆操作，请谨慎使用</div>"}]}