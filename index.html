<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 文档 - Map 接口</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 添加mammoth.js库用于解析Word文档 -->
    <script src="https://unpkg.com/mammoth@1.6.0/mammoth.browser.min.js"></script>
    <script>
        // 备用CDN加载
        window.addEventListener('load', function() {
            if (typeof mammoth === 'undefined') {
                console.log('主CDN加载失败，尝试备用CDN...');
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/mammoth@1.6.0/mammoth.browser.min.js';
                script.onload = function() {
                    console.log('备用CDN加载成功');
                };
                script.onerror = function() {
                    console.log('备用CDN也加载失败');
                };
                document.head.appendChild(script);
            } else {
                console.log('mammoth.js加载成功');
            }
        });
    </script>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-nav">
        <div class="nav-left">
            <span class="logo">API 文档</span>
            <nav class="main-nav">
                <a href="#" class="nav-item active">接口文档</a>
            </nav>
        </div>
        <div class="nav-right">
            <input type="file" id="fileUpload" accept=".json,.yaml,.yml,.md,.doc,.docx,.txt" style="display: none;">
            <button id="uploadBtn" class="upload-btn" onclick="toggleUploadArea()">上传文档</button>
            <a href="#" class="nav-link">登录</a>
            <a href="#" class="nav-link register">注册</a>
        </div>
    </header>

    <div class="container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-item active" data-target="repositories">Repositories</div>
                <div class="sidebar-item" data-target="issues">Issues</div>
                <div class="sidebar-item" data-target="pulls">Pull Requests</div>
                <div class="sidebar-item" data-target="users">Users</div>
                <div class="sidebar-item" data-target="organizations">Organizations</div>
                <div class="sidebar-item" data-target="actions">Actions</div>
                <div class="sidebar-item" data-target="commits">Commits</div>
                <div class="sidebar-item" data-target="branches">Branches</div>
                <div class="sidebar-item" data-target="releases">Releases</div>
                <div class="sidebar-item" data-target="webhooks">Webhooks</div>
                <div class="sidebar-item" data-target="search">Search</div>
                <div class="sidebar-item" data-target="gists">Gists</div>
                <div class="sidebar-item" data-target="notifications">Notifications</div>
                <div class="sidebar-item" data-target="teams">Teams</div>
                <div class="sidebar-item" data-target="apps">Apps</div>
                <div class="sidebar-item" data-target="oauth">OAuth</div>
                <div class="sidebar-item" data-target="rate-limit">Rate Limit</div>
                <div class="sidebar-item" data-target="meta">Meta</div>
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="breadcrumb">
                <span class="breadcrumb-item">Repositories</span>
            </div>

            <h1 class="page-title">Repositories</h1>
            <p class="page-subtitle">仓库管理接口</p>
            <p class="page-description">用于管理GitHub仓库的REST API接口</p>

            <!-- 文件上传区域 -->
            <div id="uploadArea" class="upload-area" style="display: none;">
                <div class="upload-zone" id="uploadZone">
                    <div class="upload-icon">📄</div>
                    <h3>拖拽文件到此处或点击上传</h3>
                    <p>支持 JSON、YAML、Markdown、Word、文本 格式的API文档文件</p>
                    <button type="button" id="selectFileBtn" class="select-file-btn">选择文件</button>
                </div>
                <div id="uploadStatus" class="upload-status"></div>
            </div>

            <!-- 获取仓库信息 -->
            <section class="api-section">
                <h2 class="section-title">GET /repos/{owner}/{repo}</h2>
                <p class="section-subtitle">获取仓库信息</p>
                <p class="method-description">获取指定仓库的详细信息，包括仓库名称、描述、星标数等</p>

                <h3>路径参数</h3>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>owner</td>
                            <td><span class="type">string</span></td>
                            <td>仓库所有者的用户名或组织名</td>
                        </tr>
                        <tr>
                            <td>repo</td>
                            <td><span class="type">string</span></td>
                            <td>仓库名称</td>
                        </tr>
                    </tbody>
                </table>

                <h3>响应示例</h3>
                <div class="code-example">
                    <pre><code>{
  "id": 1296269,
  "name": "Hello-World",
  "full_name": "octocat/Hello-World",
  "owner": {
    "login": "octocat",
    "id": 1,
    "avatar_url": "https://github.com/images/error/octocat_happy.gif"
  },
  "private": false,
  "description": "This your first repo!",
  "fork": false,
  "created_at": "2011-01-26T19:01:12Z",
  "updated_at": "2011-01-26T19:14:43Z",
  "stargazers_count": 80,
  "watchers_count": 9,
  "language": "C",
  "forks_count": 9,
  "archived": false,
  "disabled": false,
  "open_issues_count": 0,
  "license": {
    "key": "mit",
    "name": "MIT License"
  },
  "default_branch": "master"
}</code></pre>
                </div>
            </section>

            <!-- 创建仓库 -->
            <section class="api-section">
                <h2 class="section-title">POST /user/repos</h2>
                <p class="section-subtitle">创建仓库</p>
                <p class="method-description">为认证用户创建一个新的仓库</p>

                <h3>请求体参数</h3>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>必需</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>name</td>
                            <td><span class="type">string</span></td>
                            <td>是</td>
                            <td>仓库名称</td>
                        </tr>
                        <tr>
                            <td>description</td>
                            <td><span class="type">string</span></td>
                            <td>否</td>
                            <td>仓库描述</td>
                        </tr>
                        <tr>
                            <td>private</td>
                            <td><span class="type">boolean</span></td>
                            <td>否</td>
                            <td>是否为私有仓库，默认为 false</td>
                        </tr>
                        <tr>
                            <td>auto_init</td>
                            <td><span class="type">boolean</span></td>
                            <td>否</td>
                            <td>是否自动初始化仓库，默认为 false</td>
                        </tr>
                        <tr>
                            <td>gitignore_template</td>
                            <td><span class="type">string</span></td>
                            <td>否</td>
                            <td>.gitignore 模板名称</td>
                        </tr>
                        <tr>
                            <td>license_template</td>
                            <td><span class="type">string</span></td>
                            <td>否</td>
                            <td>许可证模板名称</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- 删除仓库 -->
            <section class="api-section">
                <h2 class="section-title">DELETE /repos/{owner}/{repo}</h2>
                <p class="section-subtitle">删除仓库</p>
                <p class="method-description">删除指定的仓库。删除仓库需要管理员权限</p>

                <div class="warning-box">
                    <strong>⚠️ 警告:</strong> 删除仓库是不可逆的操作，请谨慎使用
                </div>
            </section>

            <!-- 获取仓库列表 -->
            <section class="api-section">
                <h2 class="section-title">GET /user/repos</h2>
                <p class="section-subtitle">获取当前用户的仓库列表</p>
                <p class="method-description">获取认证用户的所有仓库</p>

                <h3>查询参数</h3>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>参数名</th>
                            <th>类型</th>
                            <th>默认值</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>visibility</td>
                            <td><span class="type">string</span></td>
                            <td>all</td>
                            <td>仓库可见性: all, public, private</td>
                        </tr>
                        <tr>
                            <td>affiliation</td>
                            <td><span class="type">string</span></td>
                            <td>owner,collaborator,organization_member</td>
                            <td>用户与仓库的关系</td>
                        </tr>
                        <tr>
                            <td>type</td>
                            <td><span class="type">string</span></td>
                            <td>all</td>
                            <td>仓库类型: all, owner, public, private, member</td>
                        </tr>
                        <tr>
                            <td>sort</td>
                            <td><span class="type">string</span></td>
                            <td>created</td>
                            <td>排序方式: created, updated, pushed, full_name</td>
                        </tr>
                        <tr>
                            <td>direction</td>
                            <td><span class="type">string</span></td>
                            <td>desc</td>
                            <td>排序方向: asc, desc</td>
                        </tr>
                        <tr>
                            <td>per_page</td>
                            <td><span class="type">integer</span></td>
                            <td>30</td>
                            <td>每页返回的结果数量，最大100</td>
                        </tr>
                        <tr>
                            <td>page</td>
                            <td><span class="type">integer</span></td>
                            <td>1</td>
                            <td>页码</td>
                        </tr>
                    </tbody>
                </table>
            </section>
        </main>

        <!-- 右侧相关链接 -->
        <aside class="right-sidebar">
            <div class="related-links">
                <h3>Repositories</h3>
                <ul>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}</a></li>
                    <li><a href="#" class="link">POST /user/repos</a></li>
                    <li><a href="#" class="link">DELETE /repos/{owner}/{repo}</a></li>
                    <li><a href="#" class="link">GET /user/repos</a></li>
                    <li><a href="#" class="link">PATCH /repos/{owner}/{repo}</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/contributors</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/languages</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/teams</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/tags</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/branches</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/collaborators</a></li>
                    <li><a href="#" class="link">PUT /repos/{owner}/{repo}/collaborators/{username}</a></li>
                    <li><a href="#" class="link">DELETE /repos/{owner}/{repo}/collaborators/{username}</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/topics</a></li>
                    <li><a href="#" class="link">PUT /repos/{owner}/{repo}/topics</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/stargazers</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/subscribers</a></li>
                    <li><a href="#" class="link">GET /repos/{owner}/{repo}/forks</a></li>
                    <li><a href="#" class="link">POST /repos/{owner}/{repo}/forks</a></li>
                </ul>
            </div>
        </aside>
    </div>

    <script src="script.js"></script>
</body>
</html>
