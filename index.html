<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API 文档 - Map 接口</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="top-nav">
        <div class="nav-left">
            <span class="logo">[*] 完整数据 | 打造平台</span>
            <nav class="main-nav">
                <a href="#" class="nav-item">首页</a>
                <a href="#" class="nav-item">开发教程</a>
                <a href="#" class="nav-item">地图文档</a>
                <a href="#" class="nav-item">示例</a>
                <a href="#" class="nav-item">问答</a>
                <a href="#" class="nav-item">技术分享</a>
                <a href="#" class="nav-item">专栏</a>
            </nav>
        </div>
        <div class="nav-right">
            <a href="#" class="nav-link">我的消息</a>
            <a href="#" class="nav-link">登录</a>
            <a href="#" class="nav-link register">注册</a>
        </div>
    </header>

    <div class="container">
        <!-- 左侧导航栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-item active">Map</div>
                <div class="sidebar-item">S57ShapeLayer</div>
                <div class="sidebar-item">S57VectorTileLayer</div>
                <div class="sidebar-item">SceneItem</div>
                <div class="sidebar-item">SceneItemLayer</div>
                <div class="sidebar-item">SceneLayer</div>
                <div class="sidebar-item">SceneArcItem</div>
                <div class="sidebar-item">SceneBezierCurveItem</div>
                <div class="sidebar-item">SceneCircleItem</div>
                <div class="sidebar-item">SceneCircleItem</div>
                <div class="sidebar-item">SceneEllipseItem</div>
                <div class="sidebar-item">SceneItem</div>
                <div class="sidebar-item">SceneArcCircleItem</div>
                <div class="sidebar-item">SceneOnewayPathwayItem</div>
                <div class="sidebar-item">SceneItem</div>
                <div class="sidebar-item">ScenePolygonItem</div>
                <div class="sidebar-item">ScenePolylineItem</div>
                <div class="sidebar-item">ScenePolygonItem</div>
            </div>
        </aside>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <div class="breadcrumb">
                <span class="breadcrumb-item">Map</span>
            </div>
            
            <h1 class="page-title">Map</h1>
            <p class="page-subtitle">地图类</p>
            <p class="page-description">Kind: global class</p>

            <!-- 构造函数 -->
            <section class="api-section">
                <h2 class="section-title">new Map(target, options)</h2>
                <p class="section-subtitle">创建地图实例</p>
                
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>Param</th>
                            <th>Type</th>
                            <th>Description</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>target</td>
                            <td><span class="type">string</span> | <span class="type">HTMLElement</span></td>
                            <td>容器元素</td>
                        </tr>
                        <tr>
                            <td>options</td>
                            <td><span class="type">Object</span></td>
                            <td>配置选项</td>
                        </tr>
                        <tr>
                            <td>options.center</td>
                            <td><span class="type">Array.&lt;number&gt;</span></td>
                            <td>地图中心点坐标，格式为 [x, y]，默认为地图中心，支持经纬度坐标</td>
                        </tr>
                        <tr>
                            <td>options.zoom</td>
                            <td><span class="type">number</span></td>
                            <td>缩放级别，默认为 10，范围为 1-20</td>
                        </tr>
                        <tr>
                            <td>options.viewProjection</td>
                            <td><span class="type">number</span></td>
                            <td>视图投影坐标系，默认为 4326</td>
                        </tr>
                        <tr>
                            <td>options.sceneProjection</td>
                            <td><span class="type">number</span></td>
                            <td>场景投影坐标系，默认为 3857</td>
                        </tr>
                        <tr>
                            <td>options.maxZoom</td>
                            <td><span class="type">number</span></td>
                            <td>最大缩放级别</td>
                        </tr>
                        <tr>
                            <td>options.minZoom</td>
                            <td><span class="type">number</span></td>
                            <td>最小缩放级别</td>
                        </tr>
                        <tr>
                            <td>options.snapToPixel</td>
                            <td><span class="type">number</span></td>
                            <td>像素对齐设置，提高渲染性能，默认为 true</td>
                        </tr>
                        <tr>
                            <td>options.pixelRatio</td>
                            <td><span class="type">number</span></td>
                            <td>设备像素比，默认为 1，支持高分辨率显示</td>
                        </tr>
                        <tr>
                            <td>options.moveTolerance</td>
                            <td><span class="type">number</span></td>
                            <td>移动容差，默认为 1</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <!-- 方法列表 -->
            <section class="api-section">
                <h2 class="section-title">delete</h2>
                <p class="section-subtitle">删除地图</p>
                <p class="method-description">Kind: instance method of <span class="type">Map</span></p>
            </section>

            <section class="api-section">
                <h2 class="section-title">destroy</h2>
                <p class="section-subtitle">销毁地图</p>
                <p class="method-description">Kind: instance method of <span class="type">Map</span></p>
            </section>

            <section class="api-section">
                <h2 class="section-title">getViewport</h2>
                <p class="section-subtitle">获取视窗</p>
                <p class="method-description">Kind: instance method of <span class="type">Map</span></p>
                <p class="method-returns"><strong>Returns:</strong> <span class="type">HTMLElement</span> - 视窗元素</p>
            </section>
        </main>

        <!-- 右侧相关链接 -->
        <aside class="right-sidebar">
            <div class="related-links">
                <h3>Map</h3>
                <ul>
                    <li><a href="#" class="link">new Map(target, options)</a></li>
                    <li><a href="#" class="link">delete</a></li>
                    <li><a href="#" class="link">destroy</a></li>
                    <li><a href="#" class="link">getViewport</a></li>
                    <li><a href="#" class="link">update</a></li>
                    <li><a href="#" class="link">addLayer</a></li>
                    <li><a href="#" class="link">removeLayer</a></li>
                    <li><a href="#" class="link">getLayers</a></li>
                    <li><a href="#" class="link">Render</a></li>
                    <li><a href="#" class="link">Items</a></li>
                    <li><a href="#" class="link">RenderPoint</a></li>
                    <li><a href="#" class="link">RenderRect</a></li>
                    <li><a href="#" class="link">RenderPolygon</a></li>
                    <li><a href="#" class="link">un</a></li>
                    <li><a href="#" class="link">setTransformationAnchor</a></li>
                    <li><a href="#" class="link">setCenter</a></li>
                    <li><a href="#" class="link">getCenter</a></li>
                    <li><a href="#" class="link">getZoom</a></li>
                    <li><a href="#" class="link">setZoom</a></li>
                    <li><a href="#" class="link">getExtent</a></li>
                    <li><a href="#" class="link">setRotation</a></li>
                </ul>
            </div>
        </aside>
    </div>

    <script src="script.js"></script>
</body>
</html>
