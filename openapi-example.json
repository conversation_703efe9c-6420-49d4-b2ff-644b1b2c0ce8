{"openapi": "3.0.0", "info": {"title": "电商系统API", "description": "提供商品管理、订单处理、用户管理等功能的REST API", "version": "1.0.0", "contact": {"name": "API支持", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.example.com/v1", "description": "生产环境"}, {"url": "https://staging-api.example.com/v1", "description": "测试环境"}], "paths": {"/products": {"get": {"summary": "获取商品列表", "description": "获取所有商品的分页列表", "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "limit", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20, "maximum": 100}}, {"name": "category", "in": "query", "description": "商品分类", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取商品列表", "content": {"application/json": {"example": {"code": 200, "message": "获取成功", "data": {"products": [{"id": 1, "name": "iPhone 15 Pro", "price": 7999.0, "category": "手机", "stock": 50, "description": "最新款iPhone手机"}], "pagination": {"page": 1, "limit": 20, "total": 100, "pages": 5}}}}}}}}, "post": {"summary": "创建商品", "description": "添加新的商品到系统中", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["name", "price", "category"], "properties": {"name": {"type": "string", "description": "商品名称"}, "price": {"type": "number", "description": "商品价格"}, "category": {"type": "string", "description": "商品分类"}, "description": {"type": "string", "description": "商品描述"}, "stock": {"type": "integer", "description": "库存数量"}}}, "example": {"name": "MacBook Pro 16", "price": 19999.0, "category": "笔记本电脑", "description": "专业级笔记本电脑", "stock": 30}}}}, "responses": {"201": {"description": "商品创建成功", "content": {"application/json": {"example": {"code": 201, "message": "创建成功", "data": {"id": 2, "name": "MacBook Pro 16", "price": 19999.0, "category": "笔记本电脑", "description": "专业级笔记本电脑", "stock": 30, "created_at": "2024-01-15T10:30:00Z"}}}}}}}}, "/products/{id}": {"get": {"summary": "获取商品详情", "description": "根据商品ID获取详细信息", "parameters": [{"name": "id", "in": "path", "required": true, "description": "商品ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取商品详情", "content": {"application/json": {"example": {"code": 200, "message": "获取成功", "data": {"id": 1, "name": "iPhone 15 Pro", "price": 7999.0, "category": "手机", "stock": 50, "description": "最新款iPhone手机", "images": ["https://example.com/images/iphone15pro-1.jpg", "https://example.com/images/iphone15pro-2.jpg"], "specifications": {"screen": "6.1英寸", "storage": "128GB", "color": "深空黑色"}, "created_at": "2024-01-10T08:00:00Z", "updated_at": "2024-01-15T10:30:00Z"}}}}}, "404": {"description": "商品不存在", "content": {"application/json": {"example": {"code": 404, "message": "商品不存在"}}}}}}, "put": {"summary": "更新商品信息", "description": "更新指定商品的信息", "parameters": [{"name": "id", "in": "path", "required": true, "description": "商品ID", "schema": {"type": "integer"}}], "requestBody": {"required": true, "content": {"application/json": {"example": {"name": "iPhone 15 Pro Max", "price": 8999.0, "stock": 25}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"example": {"code": 200, "message": "更新成功", "data": {"id": 1, "name": "iPhone 15 Pro Max", "price": 8999.0, "stock": 25, "updated_at": "2024-01-15T12:00:00Z"}}}}}}}, "delete": {"summary": "删除商品", "description": "从系统中删除指定商品", "parameters": [{"name": "id", "in": "path", "required": true, "description": "商品ID", "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功", "content": {"application/json": {"example": {"code": 200, "message": "商品已删除"}}}}}}}}}