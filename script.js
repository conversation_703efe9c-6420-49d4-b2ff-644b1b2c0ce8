// API 文档交互功能
document.addEventListener('DOMContentLoaded', function() {
    
    // 左侧导航栏点击事件
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有活动状态
            sidebarItems.forEach(i => i.classList.remove('active'));
            // 添加当前项的活动状态
            this.classList.add('active');
            
            // 这里可以添加切换内容的逻辑
            console.log('切换到:', this.textContent);
        });
    });
    
    // 右侧链接点击事件 - 平滑滚动到对应部分
    const rightLinks = document.querySelectorAll('.right-sidebar .link');
    rightLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetText = this.textContent.trim();
            
            // 查找对应的标题元素
            const sections = document.querySelectorAll('.section-title');
            sections.forEach(section => {
                if (section.textContent.includes(targetText)) {
                    section.scrollIntoView({ 
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // 高亮显示目标部分
                    section.style.backgroundColor = '#fff3cd';
                    setTimeout(() => {
                        section.style.backgroundColor = '#f8f9fa';
                    }, 2000);
                }
            });
        });
    });
    
    // 参数表格行悬停效果增强
    const tableRows = document.querySelectorAll('.params-table tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
            this.style.boxShadow = '0 2px 8px rgba(0,123,255,0.15)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
            this.style.boxShadow = 'none';
        });
    });
    
    // 代码类型标签点击复制功能
    const typeElements = document.querySelectorAll('.type');
    typeElements.forEach(type => {
        type.addEventListener('click', function() {
            // 复制类型文本到剪贴板
            navigator.clipboard.writeText(this.textContent).then(() => {
                // 显示复制成功提示
                const originalText = this.textContent;
                this.textContent = '已复制!';
                this.style.backgroundColor = '#d4edda';
                this.style.color = '#155724';
                
                setTimeout(() => {
                    this.textContent = originalText;
                    this.style.backgroundColor = '#e3f2fd';
                    this.style.color = '#1565c0';
                }, 1000);
            });
        });
        
        // 添加鼠标悬停提示
        type.title = '点击复制类型';
        type.style.cursor = 'pointer';
    });
    
    // 搜索功能（简单实现）
    function addSearchFunctionality() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索 API...';
        searchInput.className = 'search-input';
        searchInput.style.cssText = `
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        `;
        
        const sidebar = document.querySelector('.sidebar');
        sidebar.insertBefore(searchInput, sidebar.firstChild);
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const items = document.querySelectorAll('.sidebar-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
    
    // 添加搜索功能
    addSearchFunctionality();
    
    // 主题切换功能（可选）
    function addThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.textContent = '🌙';
        themeToggle.className = 'theme-toggle';
        themeToggle.style.cssText = `
            position: fixed;
            top: 60px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        `;
        
        document.body.appendChild(themeToggle);
        
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            this.textContent = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        });
    }
    
    // 添加主题切换按钮
    addThemeToggle();
    
    // 添加暗色主题样式
    const darkThemeStyles = `
        .dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        .dark-theme .container {
            background-color: #2d2d2d;
        }
        
        .dark-theme .sidebar,
        .dark-theme .right-sidebar {
            background-color: #252525;
            border-color: #404040;
        }
        
        .dark-theme .sidebar-item {
            color: #b0b0b0;
        }
        
        .dark-theme .sidebar-item:hover {
            background-color: #404040;
            color: #64b5f6;
        }
        
        .dark-theme .sidebar-item.active {
            background-color: #1e3a8a;
            color: #93c5fd;
        }
        
        .dark-theme .params-table {
            background-color: #2d2d2d;
            border-color: #404040;
        }
        
        .dark-theme .params-table th {
            background-color: #404040;
            color: #e0e0e0;
        }
        
        .dark-theme .params-table td {
            border-color: #404040;
        }
        
        .dark-theme .section-title {
            background-color: #404040;
            color: #e0e0e0;
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = darkThemeStyles;
    document.head.appendChild(styleSheet);
    
    console.log('API 文档页面已加载完成');
});
