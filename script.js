// API 文档交互功能
document.addEventListener('DOMContentLoaded', function() {

    // API 内容数据 - 添加更多接口的详细信息
    const apiContent = {
        repositories: {
            title: 'Repositories',
            subtitle: '仓库管理接口',
            description: '用于管理GitHub仓库的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}',
                    subtitle: '获取仓库信息',
                    description: '获取指定仓库的详细信息，包括仓库名称、描述、星标数等',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者的用户名或组织名</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "id": 1296269,
  "name": "Hello-World",
  "full_name": "octocat/Hello-World",
  "owner": {
    "login": "octocat",
    "id": 1,
    "avatar_url": "https://github.com/images/error/octocat_happy.gif"
  },
  "private": false,
  "description": "This your first repo!",
  "stargazers_count": 80,
  "watchers_count": 9,
  "language": "C",
  "forks_count": 9
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        issues: {
            title: 'Issues',
            subtitle: '问题管理接口',
            description: '用于管理GitHub Issues的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/issues',
                    subtitle: '获取仓库的Issues列表',
                    description: '获取指定仓库的所有Issues',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>state</td><td><span class="type">string</span></td><td>open</td><td>Issue状态: open, closed, all</td></tr>
                                <tr><td>labels</td><td><span class="type">string</span></td><td>-</td><td>标签过滤，多个标签用逗号分隔</td></tr>
                                <tr><td>sort</td><td><span class="type">string</span></td><td>created</td><td>排序方式: created, updated, comments</td></tr>
                                <tr><td>direction</td><td><span class="type">string</span></td><td>desc</td><td>排序方向: asc, desc</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        pulls: {
            title: 'Pull Requests',
            subtitle: '拉取请求接口',
            description: '用于管理GitHub Pull Requests的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/pulls',
                    subtitle: '获取Pull Requests列表',
                    description: '获取指定仓库的所有Pull Requests',
                    content: `
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>state</td><td><span class="type">string</span></td><td>open</td><td>PR状态: open, closed, all</td></tr>
                                <tr><td>head</td><td><span class="type">string</span></td><td>-</td><td>源分支，格式: user:ref-name</td></tr>
                                <tr><td>base</td><td><span class="type">string</span></td><td>-</td><td>目标分支</td></tr>
                                <tr><td>sort</td><td><span class="type">string</span></td><td>created</td><td>排序方式: created, updated, popularity</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        users: {
            title: 'Users',
            subtitle: '用户管理接口',
            description: '用于获取GitHub用户信息的REST API接口',
            sections: [
                {
                    title: 'GET /users/{username}',
                    subtitle: '获取用户信息',
                    description: '获取指定用户的公开信息',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>username</td><td><span class="type">string</span></td><td>GitHub用户名</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "login": "octocat",
  "id": 1,
  "avatar_url": "https://github.com/images/error/octocat_happy.gif",
  "name": "monalisa octocat",
  "company": "GitHub",
  "blog": "https://github.com/blog",
  "location": "San Francisco",
  "email": "<EMAIL>",
  "bio": "There once was...",
  "public_repos": 2,
  "public_gists": 1,
  "followers": 20,
  "following": 0,
  "created_at": "2008-01-14T04:33:35Z"
}</code></pre>
                        </div>
                    `
                }
            ]
        }
    };

    // 左侧导航栏点击事件 - 实现内容切换
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    const mainContent = document.querySelector('.main-content');

    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有活动状态
            sidebarItems.forEach(i => i.classList.remove('active'));
            // 添加当前项的活动状态
            this.classList.add('active');

            // 获取目标内容
            const target = this.getAttribute('data-target');
            const content = apiContent[target];

            if (content) {
                // 更新主要内容区域
                updateMainContent(content);
                // 更新右侧链接
                updateRightSidebar(content);
            }
        });
    });

    // 更新主要内容区域的函数
    function updateMainContent(content) {
        const breadcrumb = mainContent.querySelector('.breadcrumb-item');
        const pageTitle = mainContent.querySelector('.page-title');
        const pageSubtitle = mainContent.querySelector('.page-subtitle');
        const pageDescription = mainContent.querySelector('.page-description');

        // 更新基本信息
        breadcrumb.textContent = content.title;
        pageTitle.textContent = content.title;
        pageSubtitle.textContent = content.subtitle;
        pageDescription.textContent = content.description;

        // 清除现有的API部分
        const existingSections = mainContent.querySelectorAll('.api-section');
        existingSections.forEach(section => section.remove());

        // 添加新的API部分
        content.sections.forEach(section => {
            const sectionElement = document.createElement('section');
            sectionElement.className = 'api-section';
            sectionElement.innerHTML = `
                <h2 class="section-title">${section.title}</h2>
                <p class="section-subtitle">${section.subtitle}</p>
                <p class="method-description">${section.description}</p>
                ${section.content}
            `;
            mainContent.appendChild(sectionElement);
        });

        // 重新绑定类型标签点击事件
        bindTypeClickEvents();
    }

    // 更新右侧边栏的函数
    function updateRightSidebar(content) {
        const rightSidebar = document.querySelector('.right-sidebar');
        const relatedLinks = rightSidebar.querySelector('.related-links');

        // 更新标题
        const title = relatedLinks.querySelector('h3');
        title.textContent = content.title;

        // 更新链接列表
        const ul = relatedLinks.querySelector('ul');
        ul.innerHTML = '';

        content.sections.forEach(section => {
            const li = document.createElement('li');
            li.innerHTML = `<a href="#" class="link">${section.title}</a>`;
            ul.appendChild(li);
        });

        // 重新绑定右侧链接点击事件
        bindRightLinkEvents();
    }

    // 绑定右侧链接点击事件的函数
    function bindRightLinkEvents() {
        const rightLinks = document.querySelectorAll('.right-sidebar .link');
        rightLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetText = this.textContent.trim();

                // 查找对应的标题元素
                const sections = document.querySelectorAll('.section-title');
                sections.forEach(section => {
                    if (section.textContent.includes(targetText)) {
                        section.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // 高亮显示目标部分
                        section.style.backgroundColor = '#fff3cd';
                        setTimeout(() => {
                            section.style.backgroundColor = '#f8f9fa';
                        }, 2000);
                    }
                });
            });
        });
    }

    // 初始绑定右侧链接事件
    bindRightLinkEvents();

    // 绑定类型标签点击事件的函数
    function bindTypeClickEvents() {
        const typeElements = document.querySelectorAll('.type');
        typeElements.forEach(type => {
            // 移除之前的事件监听器（如果有的话）
            type.removeEventListener('click', typeClickHandler);
            // 添加新的事件监听器
            type.addEventListener('click', typeClickHandler);

            // 添加鼠标悬停提示
            type.title = '点击复制类型';
            type.style.cursor = 'pointer';
        });
    }

    // 类型标签点击处理函数
    function typeClickHandler() {
        // 复制类型文本到剪贴板
        navigator.clipboard.writeText(this.textContent).then(() => {
            // 显示复制成功提示
            const originalText = this.textContent;
            this.textContent = '已复制!';
            this.style.backgroundColor = '#d4edda';
            this.style.color = '#155724';

            setTimeout(() => {
                this.textContent = originalText;
                this.style.backgroundColor = '#e3f2fd';
                this.style.color = '#1565c0';
            }, 1000);
        });
    }

    // 参数表格行悬停效果增强
    function bindTableRowEvents() {
        const tableRows = document.querySelectorAll('.params-table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.boxShadow = '0 2px 8px rgba(0,123,255,0.15)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });
    }

    // 初始绑定事件
    bindTypeClickEvents();
    bindTableRowEvents();
    
    // 搜索功能（简单实现）
    function addSearchFunctionality() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索 API...';
        searchInput.className = 'search-input';
        searchInput.style.cssText = `
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        `;
        
        const sidebar = document.querySelector('.sidebar');
        sidebar.insertBefore(searchInput, sidebar.firstChild);
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const items = document.querySelectorAll('.sidebar-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
    
    // 添加搜索功能
    addSearchFunctionality();
    
    // 主题切换功能（可选）
    function addThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.textContent = '🌙';
        themeToggle.className = 'theme-toggle';
        themeToggle.style.cssText = `
            position: fixed;
            top: 60px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        `;
        
        document.body.appendChild(themeToggle);
        
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            this.textContent = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        });
    }
    
    // 添加主题切换按钮
    addThemeToggle();
    
    // 添加暗色主题样式
    const darkThemeStyles = `
        .dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        .dark-theme .container {
            background-color: #2d2d2d;
        }
        
        .dark-theme .sidebar,
        .dark-theme .right-sidebar {
            background-color: #252525;
            border-color: #404040;
        }
        
        .dark-theme .sidebar-item {
            color: #b0b0b0;
        }
        
        .dark-theme .sidebar-item:hover {
            background-color: #404040;
            color: #64b5f6;
        }
        
        .dark-theme .sidebar-item.active {
            background-color: #1e3a8a;
            color: #93c5fd;
        }
        
        .dark-theme .params-table {
            background-color: #2d2d2d;
            border-color: #404040;
        }
        
        .dark-theme .params-table th {
            background-color: #404040;
            color: #e0e0e0;
        }
        
        .dark-theme .params-table td {
            border-color: #404040;
        }
        
        .dark-theme .section-title {
            background-color: #404040;
            color: #e0e0e0;
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = darkThemeStyles;
    document.head.appendChild(styleSheet);
    
    console.log('API 文档页面已加载完成');
});
