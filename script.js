// API 文档交互功能
document.addEventListener('DOMContentLoaded', function() {

    // API 内容数据 - 添加更多接口的详细信息
    const apiContent = {
        repositories: {
            title: 'Repositories',
            subtitle: '仓库管理接口',
            description: '用于管理GitHub仓库的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}',
                    subtitle: '获取仓库信息',
                    description: '获取指定仓库的详细信息，包括仓库名称、描述、星标数等',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者的用户名或组织名</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "id": 1296269,
  "name": "Hello-World",
  "full_name": "octocat/Hello-World",
  "owner": {
    "login": "octocat",
    "id": 1,
    "avatar_url": "https://github.com/images/error/octocat_happy.gif"
  },
  "private": false,
  "description": "This your first repo!",
  "stargazers_count": 80,
  "watchers_count": 9,
  "language": "C",
  "forks_count": 9
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        issues: {
            title: 'Issues',
            subtitle: '问题管理接口',
            description: '用于管理GitHub Issues的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/issues',
                    subtitle: '获取仓库的Issues列表',
                    description: '获取指定仓库的所有Issues',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>state</td><td><span class="type">string</span></td><td>open</td><td>Issue状态: open, closed, all</td></tr>
                                <tr><td>labels</td><td><span class="type">string</span></td><td>-</td><td>标签过滤，多个标签用逗号分隔</td></tr>
                                <tr><td>sort</td><td><span class="type">string</span></td><td>created</td><td>排序方式: created, updated, comments</td></tr>
                                <tr><td>direction</td><td><span class="type">string</span></td><td>desc</td><td>排序方向: asc, desc</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        pulls: {
            title: 'Pull Requests',
            subtitle: '拉取请求接口',
            description: '用于管理GitHub Pull Requests的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/pulls',
                    subtitle: '获取Pull Requests列表',
                    description: '获取指定仓库的所有Pull Requests',
                    content: `
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>state</td><td><span class="type">string</span></td><td>open</td><td>PR状态: open, closed, all</td></tr>
                                <tr><td>head</td><td><span class="type">string</span></td><td>-</td><td>源分支，格式: user:ref-name</td></tr>
                                <tr><td>base</td><td><span class="type">string</span></td><td>-</td><td>目标分支</td></tr>
                                <tr><td>sort</td><td><span class="type">string</span></td><td>created</td><td>排序方式: created, updated, popularity</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        users: {
            title: 'Users',
            subtitle: '用户管理接口',
            description: '用于获取GitHub用户信息的REST API接口',
            sections: [
                {
                    title: 'GET /users/{username}',
                    subtitle: '获取用户信息',
                    description: '获取指定用户的公开信息',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>username</td><td><span class="type">string</span></td><td>GitHub用户名</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "login": "octocat",
  "id": 1,
  "avatar_url": "https://github.com/images/error/octocat_happy.gif",
  "name": "monalisa octocat",
  "company": "GitHub",
  "blog": "https://github.com/blog",
  "location": "San Francisco",
  "email": "<EMAIL>",
  "bio": "There once was...",
  "public_repos": 2,
  "public_gists": 1,
  "followers": 20,
  "following": 0,
  "created_at": "2008-01-14T04:33:35Z"
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        organizations: {
            title: 'Organizations',
            subtitle: '组织管理接口',
            description: '用于管理GitHub组织的REST API接口',
            sections: [
                {
                    title: 'GET /orgs/{org}',
                    subtitle: '获取组织信息',
                    description: '获取指定组织的详细信息',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>org</td><td><span class="type">string</span></td><td>组织名称</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "login": "github",
  "id": 1,
  "url": "https://api.github.com/orgs/github",
  "repos_url": "https://api.github.com/orgs/github/repos",
  "events_url": "https://api.github.com/orgs/github/events",
  "hooks_url": "https://api.github.com/orgs/github/hooks",
  "issues_url": "https://api.github.com/orgs/github/issues",
  "members_url": "https://api.github.com/orgs/github/members{/member}",
  "public_members_url": "https://api.github.com/orgs/github/public_members{/member}",
  "avatar_url": "https://github.com/images/error/octocat_happy.gif",
  "description": "A great organization",
  "name": "github",
  "company": null,
  "blog": "https://github.com/blog",
  "location": "San Francisco",
  "email": "<EMAIL>",
  "public_repos": 2,
  "public_gists": 1,
  "followers": 20,
  "following": 0,
  "html_url": "https://github.com/octocat",
  "created_at": "2008-01-14T04:33:35Z",
  "type": "Organization"
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        actions: {
            title: 'Actions',
            subtitle: 'GitHub Actions接口',
            description: '用于管理GitHub Actions工作流的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/actions/workflows',
                    subtitle: '获取工作流列表',
                    description: '获取仓库中的所有工作流',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>per_page</td><td><span class="type">integer</span></td><td>30</td><td>每页返回的结果数量</td></tr>
                                <tr><td>page</td><td><span class="type">integer</span></td><td>1</td><td>页码</td></tr>
                            </tbody>
                        </table>
                    `
                },
                {
                    title: 'POST /repos/{owner}/{repo}/actions/workflows/{workflow_id}/dispatches',
                    subtitle: '触发工作流',
                    description: '手动触发工作流运行',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                                <tr><td>workflow_id</td><td><span class="type">integer</span></td><td>工作流ID</td></tr>
                            </tbody>
                        </table>
                        <h3>请求体参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>ref</td><td><span class="type">string</span></td><td>是</td><td>分支或标签引用</td></tr>
                                <tr><td>inputs</td><td><span class="type">object</span></td><td>否</td><td>工作流输入参数</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        commits: {
            title: 'Commits',
            subtitle: '提交管理接口',
            description: '用于管理GitHub提交的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/commits',
                    subtitle: '获取提交列表',
                    description: '获取仓库的提交历史',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>sha</td><td><span class="type">string</span></td><td>-</td><td>分支或提交SHA</td></tr>
                                <tr><td>path</td><td><span class="type">string</span></td><td>-</td><td>文件路径过滤</td></tr>
                                <tr><td>author</td><td><span class="type">string</span></td><td>-</td><td>作者过滤</td></tr>
                                <tr><td>since</td><td><span class="type">string</span></td><td>-</td><td>起始时间 (ISO 8601)</td></tr>
                                <tr><td>until</td><td><span class="type">string</span></td><td>-</td><td>结束时间 (ISO 8601)</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        branches: {
            title: 'Branches',
            subtitle: '分支管理接口',
            description: '用于管理GitHub分支的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/branches',
                    subtitle: '获取分支列表',
                    description: '获取仓库的所有分支',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>protected</td><td><span class="type">boolean</span></td><td>-</td><td>是否只返回受保护的分支</td></tr>
                                <tr><td>per_page</td><td><span class="type">integer</span></td><td>30</td><td>每页返回的结果数量</td></tr>
                                <tr><td>page</td><td><span class="type">integer</span></td><td>1</td><td>页码</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        releases: {
            title: 'Releases',
            subtitle: '发布管理接口',
            description: '用于管理GitHub发布版本的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/releases',
                    subtitle: '获取发布列表',
                    description: '获取仓库的所有发布版本',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>[
  {
    "id": 1,
    "tag_name": "v1.0.0",
    "target_commitish": "master",
    "name": "v1.0.0",
    "body": "Description of the release",
    "draft": false,
    "prerelease": false,
    "created_at": "2013-02-27T19:35:32Z",
    "published_at": "2013-02-27T19:35:32Z",
    "author": {
      "login": "octocat",
      "id": 1,
      "avatar_url": "https://github.com/images/error/octocat_happy.gif"
    },
    "assets": []
  }
]</code></pre>
                        </div>
                    `
                }
            ]
        },
        webhooks: {
            title: 'Webhooks',
            subtitle: 'Webhook管理接口',
            description: '用于管理GitHub Webhooks的REST API接口',
            sections: [
                {
                    title: 'GET /repos/{owner}/{repo}/hooks',
                    subtitle: '获取Webhook列表',
                    description: '获取仓库的所有Webhooks',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>owner</td><td><span class="type">string</span></td><td>仓库所有者</td></tr>
                                <tr><td>repo</td><td><span class="type">string</span></td><td>仓库名称</td></tr>
                            </tbody>
                        </table>
                    `
                },
                {
                    title: 'POST /repos/{owner}/{repo}/hooks',
                    subtitle: '创建Webhook',
                    description: '为仓库创建新的Webhook',
                    content: `
                        <h3>请求体参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>name</td><td><span class="type">string</span></td><td>是</td><td>Webhook名称，通常为 "web"</td></tr>
                                <tr><td>config</td><td><span class="type">object</span></td><td>是</td><td>Webhook配置</td></tr>
                                <tr><td>config.url</td><td><span class="type">string</span></td><td>是</td><td>接收Webhook的URL</td></tr>
                                <tr><td>config.content_type</td><td><span class="type">string</span></td><td>否</td><td>内容类型: json 或 form</td></tr>
                                <tr><td>config.secret</td><td><span class="type">string</span></td><td>否</td><td>用于验证的密钥</td></tr>
                                <tr><td>events</td><td><span class="type">array</span></td><td>否</td><td>触发事件列表</td></tr>
                                <tr><td>active</td><td><span class="type">boolean</span></td><td>否</td><td>是否激活，默认为 true</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        search: {
            title: 'Search',
            subtitle: '搜索接口',
            description: '用于搜索GitHub内容的REST API接口',
            sections: [
                {
                    title: 'GET /search/repositories',
                    subtitle: '搜索仓库',
                    description: '根据关键词搜索仓库',
                    content: `
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>q</td><td><span class="type">string</span></td><td>是</td><td>搜索关键词</td></tr>
                                <tr><td>sort</td><td><span class="type">string</span></td><td>否</td><td>排序方式: stars, forks, help-wanted-issues, updated</td></tr>
                                <tr><td>order</td><td><span class="type">string</span></td><td>否</td><td>排序顺序: asc, desc</td></tr>
                                <tr><td>per_page</td><td><span class="type">integer</span></td><td>否</td><td>每页返回的结果数量，最大100</td></tr>
                                <tr><td>page</td><td><span class="type">integer</span></td><td>否</td><td>页码</td></tr>
                            </tbody>
                        </table>
                        <h3>搜索语法示例</h3>
                        <div class="code-example">
                            <pre><code>// 搜索包含 "react" 的仓库
q=react

// 搜索用户 "facebook" 的仓库
q=user:facebook

// 搜索语言为 JavaScript 的仓库
q=language:javascript

// 搜索星标数大于1000的仓库
q=stars:>1000

// 组合搜索
q=react+language:javascript+stars:>1000</code></pre>
                        </div>
                    `
                }
            ]
        },
        gists: {
            title: 'Gists',
            subtitle: 'Gist管理接口',
            description: '用于管理GitHub Gists的REST API接口',
            sections: [
                {
                    title: 'GET /gists',
                    subtitle: '获取Gist列表',
                    description: '获取当前用户的所有Gists',
                    content: `
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>since</td><td><span class="type">string</span></td><td>-</td><td>起始时间 (ISO 8601)</td></tr>
                                <tr><td>per_page</td><td><span class="type">integer</span></td><td>30</td><td>每页返回的结果数量</td></tr>
                                <tr><td>page</td><td><span class="type">integer</span></td><td>1</td><td>页码</td></tr>
                            </tbody>
                        </table>
                    `
                },
                {
                    title: 'POST /gists',
                    subtitle: '创建Gist',
                    description: '创建新的Gist',
                    content: `
                        <h3>请求体参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>files</td><td><span class="type">object</span></td><td>是</td><td>文件对象，键为文件名</td></tr>
                                <tr><td>description</td><td><span class="type">string</span></td><td>否</td><td>Gist描述</td></tr>
                                <tr><td>public</td><td><span class="type">boolean</span></td><td>否</td><td>是否公开，默认为 false</td></tr>
                            </tbody>
                        </table>
                        <h3>请求示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "description": "Example of a gist",
  "public": true,
  "files": {
    "README.md": {
      "content": "Hello World"
    },
    "hello.py": {
      "content": "print('Hello, World!')"
    }
  }
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        notifications: {
            title: 'Notifications',
            subtitle: '通知管理接口',
            description: '用于管理GitHub通知的REST API接口',
            sections: [
                {
                    title: 'GET /notifications',
                    subtitle: '获取通知列表',
                    description: '获取当前用户的所有通知',
                    content: `
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>all</td><td><span class="type">boolean</span></td><td>false</td><td>是否显示所有通知（包括已读）</td></tr>
                                <tr><td>participating</td><td><span class="type">boolean</span></td><td>false</td><td>是否只显示参与的通知</td></tr>
                                <tr><td>since</td><td><span class="type">string</span></td><td>-</td><td>起始时间 (ISO 8601)</td></tr>
                                <tr><td>before</td><td><span class="type">string</span></td><td>-</td><td>结束时间 (ISO 8601)</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        teams: {
            title: 'Teams',
            subtitle: '团队管理接口',
            description: '用于管理GitHub团队的REST API接口',
            sections: [
                {
                    title: 'GET /orgs/{org}/teams',
                    subtitle: '获取组织团队列表',
                    description: '获取组织的所有团队',
                    content: `
                        <h3>路径参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>org</td><td><span class="type">string</span></td><td>组织名称</td></tr>
                            </tbody>
                        </table>
                        <h3>查询参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>默认值</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>per_page</td><td><span class="type">integer</span></td><td>30</td><td>每页返回的结果数量</td></tr>
                                <tr><td>page</td><td><span class="type">integer</span></td><td>1</td><td>页码</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        apps: {
            title: 'Apps',
            subtitle: 'GitHub应用接口',
            description: '用于管理GitHub应用的REST API接口',
            sections: [
                {
                    title: 'GET /app',
                    subtitle: '获取应用信息',
                    description: '获取当前GitHub应用的信息',
                    content: `
                        <div class="warning-box">
                            <strong>🔐 认证要求:</strong> 需要使用JWT令牌进行认证
                        </div>
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "id": 1,
  "slug": "octoapp",
  "node_id": "MDExOkludGVncmF0aW9uMQ==",
  "owner": {
    "login": "github",
    "id": 1,
    "avatar_url": "https://github.com/images/error/octocat_happy.gif"
  },
  "name": "Octocat App",
  "description": "",
  "external_url": "https://example.com",
  "html_url": "https://github.com/apps/octoapp",
  "created_at": "2017-07-08T16:18:44-04:00",
  "updated_at": "2017-07-08T16:18:44-04:00",
  "permissions": {
    "metadata": "read",
    "contents": "read",
    "issues": "write"
  },
  "events": [
    "push",
    "pull_request"
  ]
}</code></pre>
                        </div>
                    `
                }
            ]
        },
        oauth: {
            title: 'OAuth',
            subtitle: 'OAuth认证接口',
            description: '用于OAuth认证流程的REST API接口',
            sections: [
                {
                    title: 'POST /login/oauth/access_token',
                    subtitle: '获取访问令牌',
                    description: '使用授权码获取访问令牌',
                    content: `
                        <h3>请求体参数</h3>
                        <table class="params-table">
                            <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>client_id</td><td><span class="type">string</span></td><td>是</td><td>应用的客户端ID</td></tr>
                                <tr><td>client_secret</td><td><span class="type">string</span></td><td>是</td><td>应用的客户端密钥</td></tr>
                                <tr><td>code</td><td><span class="type">string</span></td><td>是</td><td>从授权回调中获得的代码</td></tr>
                                <tr><td>redirect_uri</td><td><span class="type">string</span></td><td>否</td><td>重定向URI</td></tr>
                            </tbody>
                        </table>
                        <div class="warning-box">
                            <strong>🔒 安全提示:</strong> 客户端密钥应该保密，不要在客户端代码中暴露
                        </div>
                    `
                }
            ]
        },
        'rate-limit': {
            title: 'Rate Limit',
            subtitle: '速率限制接口',
            description: '用于查询API速率限制的REST API接口',
            sections: [
                {
                    title: 'GET /rate_limit',
                    subtitle: '获取速率限制状态',
                    description: '获取当前用户的API速率限制信息',
                    content: `
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "resources": {
    "core": {
      "limit": 5000,
      "remaining": 4999,
      "reset": 1372700873,
      "used": 1
    },
    "search": {
      "limit": 30,
      "remaining": 18,
      "reset": 1372697452,
      "used": 12
    },
    "graphql": {
      "limit": 5000,
      "remaining": 4993,
      "reset": 1372700389,
      "used": 7
    }
  },
  "rate": {
    "limit": 5000,
    "remaining": 4999,
    "reset": 1372700873,
    "used": 1
  }
}</code></pre>
                        </div>
                        <h3>速率限制说明</h3>
                        <table class="params-table">
                            <thead><tr><th>字段</th><th>描述</th></tr></thead>
                            <tbody>
                                <tr><td>limit</td><td>每小时的请求限制</td></tr>
                                <tr><td>remaining</td><td>当前小时内剩余的请求次数</td></tr>
                                <tr><td>reset</td><td>限制重置的时间戳</td></tr>
                                <tr><td>used</td><td>当前小时内已使用的请求次数</td></tr>
                            </tbody>
                        </table>
                    `
                }
            ]
        },
        meta: {
            title: 'Meta',
            subtitle: 'GitHub元信息接口',
            description: '用于获取GitHub服务元信息的REST API接口',
            sections: [
                {
                    title: 'GET /meta',
                    subtitle: '获取GitHub元信息',
                    description: '获取GitHub服务的元信息，包括IP地址范围等',
                    content: `
                        <h3>响应示例</h3>
                        <div class="code-example">
                            <pre><code>{
  "verifiable_password_authentication": true,
  "ssh_key_fingerprints": {
    "SHA256_RSA": "nThbg6kXUpJWGl7E1IGOCspRomTxdCARLviKw6E5SY8",
    "SHA256_ECDSA": "p2QAMXNIC1TJYWeIOttrVc98/R1BUFWu3/LiyKgUfQM",
    "SHA256_ED25519": "+DiY3wvvV6TuJJhbpZisF/zLDA0zPMSvHdkr4UvCOqU"
  },
  "hooks": [
    "************/22"
  ],
  "web": [
    "************/22"
  ],
  "api": [
    "************/22"
  ],
  "git": [
    "************/22"
  ],
  "pages": [
    "**************/32",
    "**************/32"
  ],
  "importer": [
    "***********",
    "************"
  ]
}</code></pre>
                        </div>
                    `
                }
            ]
        }
    };

    // 左侧导航栏点击事件 - 实现内容切换
    const sidebarItems = document.querySelectorAll('.sidebar-item');
    const mainContent = document.querySelector('.main-content');

    sidebarItems.forEach(item => {
        item.addEventListener('click', function() {
            // 移除所有活动状态
            sidebarItems.forEach(i => i.classList.remove('active'));
            // 添加当前项的活动状态
            this.classList.add('active');

            // 获取目标内容
            const target = this.getAttribute('data-target');
            const content = apiContent[target];

            if (content) {
                // 更新主要内容区域
                updateMainContent(content);
                // 更新右侧链接
                updateRightSidebar(content);
            }
        });
    });

    // 更新主要内容区域的函数
    function updateMainContent(content) {
        const breadcrumb = mainContent.querySelector('.breadcrumb-item');
        const pageTitle = mainContent.querySelector('.page-title');
        const pageSubtitle = mainContent.querySelector('.page-subtitle');
        const pageDescription = mainContent.querySelector('.page-description');

        // 更新基本信息
        breadcrumb.textContent = content.title;
        pageTitle.textContent = content.title;
        pageSubtitle.textContent = content.subtitle;
        pageDescription.textContent = content.description;

        // 清除现有的API部分
        const existingSections = mainContent.querySelectorAll('.api-section');
        existingSections.forEach(section => section.remove());

        // 添加新的API部分
        content.sections.forEach(section => {
            const sectionElement = document.createElement('section');
            sectionElement.className = 'api-section';
            sectionElement.innerHTML = `
                <h2 class="section-title">${section.title}</h2>
                <p class="section-subtitle">${section.subtitle}</p>
                <p class="method-description">${section.description}</p>
                ${section.content}
            `;
            mainContent.appendChild(sectionElement);
        });

        // 重新绑定类型标签点击事件
        bindTypeClickEvents();
    }

    // 更新右侧边栏的函数
    function updateRightSidebar(content) {
        const rightSidebar = document.querySelector('.right-sidebar');
        const relatedLinks = rightSidebar.querySelector('.related-links');

        // 更新标题
        const title = relatedLinks.querySelector('h3');
        title.textContent = content.title;

        // 更新链接列表
        const ul = relatedLinks.querySelector('ul');
        ul.innerHTML = '';

        content.sections.forEach(section => {
            const li = document.createElement('li');
            li.innerHTML = `<a href="#" class="link">${section.title}</a>`;
            ul.appendChild(li);
        });

        // 重新绑定右侧链接点击事件
        bindRightLinkEvents();
    }

    // 绑定右侧链接点击事件的函数
    function bindRightLinkEvents() {
        const rightLinks = document.querySelectorAll('.right-sidebar .link');
        rightLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetText = this.textContent.trim();

                // 查找对应的标题元素
                const sections = document.querySelectorAll('.section-title');
                sections.forEach(section => {
                    if (section.textContent.includes(targetText)) {
                        section.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // 高亮显示目标部分
                        section.style.backgroundColor = '#fff3cd';
                        setTimeout(() => {
                            section.style.backgroundColor = '#f8f9fa';
                        }, 2000);
                    }
                });
            });
        });
    }

    // 初始绑定右侧链接事件
    bindRightLinkEvents();

    // 绑定类型标签点击事件的函数
    function bindTypeClickEvents() {
        const typeElements = document.querySelectorAll('.type');
        typeElements.forEach(type => {
            // 移除之前的事件监听器（如果有的话）
            type.removeEventListener('click', typeClickHandler);
            // 添加新的事件监听器
            type.addEventListener('click', typeClickHandler);

            // 添加鼠标悬停提示
            type.title = '点击复制类型';
            type.style.cursor = 'pointer';
        });
    }

    // 类型标签点击处理函数
    function typeClickHandler() {
        // 复制类型文本到剪贴板
        navigator.clipboard.writeText(this.textContent).then(() => {
            // 显示复制成功提示
            const originalText = this.textContent;
            this.textContent = '已复制!';
            this.style.backgroundColor = '#d4edda';
            this.style.color = '#155724';

            setTimeout(() => {
                this.textContent = originalText;
                this.style.backgroundColor = '#e3f2fd';
                this.style.color = '#1565c0';
            }, 1000);
        });
    }

    // 参数表格行悬停效果增强
    function bindTableRowEvents() {
        const tableRows = document.querySelectorAll('.params-table tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.boxShadow = '0 2px 8px rgba(0,123,255,0.15)';
            });

            row.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
                this.style.boxShadow = 'none';
            });
        });
    }

    // 初始绑定事件
    bindTypeClickEvents();
    bindTableRowEvents();

    // 文件上传功能
    initFileUpload();

    // 初始化文件上传功能
    function initFileUpload() {
        const uploadBtn = document.getElementById('uploadBtn');
        const fileUpload = document.getElementById('fileUpload');
        const uploadArea = document.getElementById('uploadArea');
        const uploadZone = document.getElementById('uploadZone');
        const selectFileBtn = document.getElementById('selectFileBtn');
        const uploadStatus = document.getElementById('uploadStatus');

        // 上传按钮点击事件
        uploadBtn.addEventListener('click', function(e) {
            e.preventDefault();
            const isVisible = uploadArea.style.display !== 'none';
            uploadArea.style.display = isVisible ? 'none' : 'block';
        });

        // 选择文件按钮点击事件
        selectFileBtn.addEventListener('click', function() {
            fileUpload.click();
        });

        // 文件选择事件
        fileUpload.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });

        // 拖拽上传功能
        uploadZone.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadZone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadZone.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        // 处理文件上传
        function handleFileUpload(file) {
            showUploadStatus('loading', '正在解析文件...');

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const content = e.target.result;
                    const fileExtension = file.name.split('.').pop().toLowerCase();

                    let parsedData;
                    switch (fileExtension) {
                        case 'json':
                            parsedData = parseJsonApiDoc(content);
                            break;
                        case 'yaml':
                        case 'yml':
                            parsedData = parseYamlApiDoc(content);
                            break;
                        case 'md':
                            parsedData = parseMarkdownApiDoc(content);
                            break;
                        case 'doc':
                        case 'docx':
                            // Word文档需要特殊处理，使用ArrayBuffer
                            parseWordDocument(file);
                            return; // 提前返回，因为Word解析是异步的
                        default:
                            throw new Error('不支持的文件格式');
                    }

                    if (parsedData) {
                        updateApiContentFromFile(parsedData);
                        showUploadStatus('success', `文件 "${file.name}" 解析成功！已更新API文档内容。`);
                        uploadArea.style.display = 'none';
                    }
                } catch (error) {
                    showUploadStatus('error', `文件解析失败: ${error.message}`);
                }
            };

            reader.onerror = function() {
                showUploadStatus('error', '文件读取失败');
            };

            // 对于非Word文档，使用文本方式读取
            if (file.name.toLowerCase().endsWith('.doc') || file.name.toLowerCase().endsWith('.docx')) {
                reader.readAsArrayBuffer(file);
            } else {
                reader.readAsText(file);
            }
        }

        // 解析Word文档
        function parseWordDocument(file) {
            showUploadStatus('loading', '正在解析Word文档...');

            const reader = new FileReader();
            reader.onload = function(e) {
                const arrayBuffer = e.target.result;

                // 检查mammoth库是否可用
                if (typeof mammoth === 'undefined') {
                    showUploadStatus('error', 'Word文档解析库未加载，请刷新页面重试');
                    return;
                }

                // 使用mammoth.js解析Word文档
                mammoth.extractRawText({arrayBuffer: arrayBuffer})
                    .then(function(result) {
                        try {
                            const text = result.value;
                            const parsedData = parseWordApiDoc(text, file.name);

                            if (parsedData) {
                                updateApiContentFromFile(parsedData);
                                showUploadStatus('success', `Word文档 "${file.name}" 解析成功！已更新API文档内容。`);
                                uploadArea.style.display = 'none';
                            }
                        } catch (error) {
                            showUploadStatus('error', `Word文档解析失败: ${error.message}`);
                        }
                    })
                    .catch(function(error) {
                        showUploadStatus('error', `Word文档读取失败: ${error.message}`);
                    });
            };

            reader.onerror = function() {
                showUploadStatus('error', 'Word文档读取失败');
            };

            reader.readAsArrayBuffer(file);
        }

        // 显示上传状态
        function showUploadStatus(type, message) {
            uploadStatus.className = `upload-status ${type}`;
            uploadStatus.textContent = message;

            if (type === 'success') {
                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 5000);
            }
        }
    }

    // 搜索功能（简单实现）
    function addSearchFunctionality() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.placeholder = '搜索 API...';
        searchInput.className = 'search-input';
        searchInput.style.cssText = `
            width: 100%;
            padding: 8px 12px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 14px;
        `;
        
        const sidebar = document.querySelector('.sidebar');
        sidebar.insertBefore(searchInput, sidebar.firstChild);
        
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const items = document.querySelectorAll('.sidebar-item');
            
            items.forEach(item => {
                const text = item.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
    
    // 添加搜索功能
    addSearchFunctionality();

    // 解析JSON格式的API文档
    function parseJsonApiDoc(content) {
        const data = JSON.parse(content);

        // 支持OpenAPI/Swagger格式
        if (data.openapi || data.swagger) {
            return parseOpenApiDoc(data);
        }

        // 支持自定义JSON格式
        if (data.apis || data.endpoints) {
            return parseCustomJsonDoc(data);
        }

        // 直接使用我们的格式
        if (data.title && data.sections) {
            return {
                'uploaded-content': {
                    title: data.title,
                    subtitle: data.subtitle || '上传的API文档',
                    description: data.description || '从上传文件解析的API文档内容',
                    sections: data.sections
                }
            };
        }

        throw new Error('无法识别的JSON格式');
    }

    // 解析OpenAPI/Swagger文档
    function parseOpenApiDoc(data) {
        const title = data.info?.title || '上传的API文档';
        const description = data.info?.description || '从OpenAPI文档解析的内容';
        const sections = [];

        if (data.paths) {
            Object.keys(data.paths).forEach(path => {
                const pathData = data.paths[path];
                Object.keys(pathData).forEach(method => {
                    const operation = pathData[method];
                    if (operation && typeof operation === 'object') {
                        sections.push({
                            title: `${method.toUpperCase()} ${path}`,
                            subtitle: operation.summary || '接口方法',
                            description: operation.description || '从OpenAPI文档解析的接口',
                            content: generateOpenApiContent(operation, path, method)
                        });
                    }
                });
            });
        }

        return {
            'uploaded-content': {
                title: title,
                subtitle: 'OpenAPI文档',
                description: description,
                sections: sections
            }
        };
    }

    // 生成OpenAPI内容HTML
    function generateOpenApiContent(operation, path, method) {
        let content = '';

        // 参数表格
        if (operation.parameters && operation.parameters.length > 0) {
            content += `
                <h3>参数</h3>
                <table class="params-table">
                    <thead><tr><th>参数名</th><th>位置</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                    <tbody>
            `;

            operation.parameters.forEach(param => {
                content += `
                    <tr>
                        <td>${param.name}</td>
                        <td><span class="type">${param.in}</span></td>
                        <td><span class="type">${param.schema?.type || param.type || 'string'}</span></td>
                        <td>${param.required ? '是' : '否'}</td>
                        <td>${param.description || '-'}</td>
                    </tr>
                `;
            });

            content += '</tbody></table>';
        }

        // 请求体
        if (operation.requestBody) {
            content += '<h3>请求体</h3>';
            const mediaType = operation.requestBody.content?.['application/json'];
            if (mediaType?.schema) {
                content += `<p>Content-Type: <span class="type">application/json</span></p>`;
                if (mediaType.example) {
                    content += `
                        <div class="code-example">
                            <pre><code>${JSON.stringify(mediaType.example, null, 2)}</code></pre>
                        </div>
                    `;
                }
            }
        }

        // 响应
        if (operation.responses) {
            content += '<h3>响应</h3>';
            Object.keys(operation.responses).forEach(statusCode => {
                const response = operation.responses[statusCode];
                content += `<h4>状态码 ${statusCode}</h4>`;
                content += `<p>${response.description || '响应描述'}</p>`;

                if (response.content?.['application/json']?.example) {
                    content += `
                        <div class="code-example">
                            <pre><code>${JSON.stringify(response.content['application/json'].example, null, 2)}</code></pre>
                        </div>
                    `;
                }
            });
        }

        return content;
    }

    // 解析自定义JSON格式
    function parseCustomJsonDoc(data) {
        const title = data.title || '上传的API文档';
        const sections = [];

        const apis = data.apis || data.endpoints || [];
        apis.forEach(api => {
            sections.push({
                title: `${api.method || 'GET'} ${api.path || api.url}`,
                subtitle: api.name || api.summary || '接口方法',
                description: api.description || '从自定义JSON解析的接口',
                content: generateCustomApiContent(api)
            });
        });

        return {
            'uploaded-content': {
                title: title,
                subtitle: '自定义API文档',
                description: data.description || '从自定义JSON文档解析的内容',
                sections: sections
            }
        };
    }

    // 生成自定义API内容HTML
    function generateCustomApiContent(api) {
        let content = '';

        if (api.parameters && api.parameters.length > 0) {
            content += `
                <h3>参数</h3>
                <table class="params-table">
                    <thead><tr><th>参数名</th><th>类型</th><th>必需</th><th>描述</th></tr></thead>
                    <tbody>
            `;

            api.parameters.forEach(param => {
                content += `
                    <tr>
                        <td>${param.name}</td>
                        <td><span class="type">${param.type || 'string'}</span></td>
                        <td>${param.required ? '是' : '否'}</td>
                        <td>${param.description || '-'}</td>
                    </tr>
                `;
            });

            content += '</tbody></table>';
        }

        if (api.example || api.response) {
            content += '<h3>响应示例</h3>';
            const example = api.example || api.response;
            content += `
                <div class="code-example">
                    <pre><code>${typeof example === 'string' ? example : JSON.stringify(example, null, 2)}</code></pre>
                </div>
            `;
        }

        return content;
    }

    // 解析YAML格式的API文档（简单实现）
    function parseYamlApiDoc(content) {
        try {
            // 简单的YAML解析（仅支持基本格式）
            const lines = content.split('\n');
            const data = {};
            let currentSection = null;
            let currentKey = null;

            lines.forEach(line => {
                line = line.trim();
                if (line.startsWith('#') || line === '') return;

                if (line.includes(':') && !line.startsWith(' ')) {
                    const [key, value] = line.split(':').map(s => s.trim());
                    if (value) {
                        data[key] = value.replace(/['"]/g, '');
                    } else {
                        currentSection = key;
                        data[currentSection] = {};
                    }
                }
            });

            // 如果解析出的数据包含OpenAPI结构，使用OpenAPI解析器
            if (data.openapi || data.swagger) {
                return parseOpenApiDoc(data);
            }

            // 否则创建简单的文档结构
            return {
                'uploaded-content': {
                    title: data.title || 'YAML API文档',
                    subtitle: 'YAML格式文档',
                    description: data.description || '从YAML文件解析的API文档',
                    sections: [{
                        title: 'YAML内容',
                        subtitle: '解析的YAML数据',
                        description: '从上传的YAML文件中解析的内容',
                        content: `
                            <div class="code-example">
                                <pre><code>${content}</code></pre>
                            </div>
                        `
                    }]
                }
            };
        } catch (error) {
            throw new Error('YAML解析失败: ' + error.message);
        }
    }

    // 解析Markdown格式的API文档
    function parseMarkdownApiDoc(content) {
        const sections = [];
        const lines = content.split('\n');
        let currentSection = null;
        let currentContent = '';

        lines.forEach(line => {
            // 检测标题
            if (line.startsWith('# ') || line.startsWith('## ') || line.startsWith('### ')) {
                // 保存上一个section
                if (currentSection) {
                    sections.push({
                        title: currentSection.title,
                        subtitle: currentSection.subtitle || 'Markdown内容',
                        description: currentSection.description || '从Markdown解析的内容',
                        content: convertMarkdownToHtml(currentContent)
                    });
                }

                // 开始新的section
                const level = line.match(/^#+/)[0].length;
                const title = line.replace(/^#+\s*/, '');

                currentSection = {
                    title: title,
                    subtitle: level === 1 ? 'API文档' : level === 2 ? 'API接口' : '接口详情',
                    description: '从Markdown文档解析的内容'
                };
                currentContent = '';
            } else {
                currentContent += line + '\n';
            }
        });

        // 保存最后一个section
        if (currentSection) {
            sections.push({
                title: currentSection.title,
                subtitle: currentSection.subtitle,
                description: currentSection.description,
                content: convertMarkdownToHtml(currentContent)
            });
        }

        return {
            'uploaded-content': {
                title: 'Markdown API文档',
                subtitle: 'Markdown格式文档',
                description: '从Markdown文件解析的API文档',
                sections: sections.length > 0 ? sections : [{
                    title: 'Markdown内容',
                    subtitle: '文档内容',
                    description: '完整的Markdown文档内容',
                    content: convertMarkdownToHtml(content)
                }]
            }
        };
    }

    // 简单的Markdown到HTML转换
    function convertMarkdownToHtml(markdown) {
        let html = markdown
            // 代码块
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<div class="code-example"><pre><code>$2</code></pre></div>')
            .replace(/`([^`]+)`/g, '<span class="type">$1</span>')
            // 表格（简单支持）
            .replace(/\|(.+)\|\n\|[-\s|]+\|\n((?:\|.+\|\n?)*)/g, function(match, header, rows) {
                const headers = header.split('|').map(h => h.trim()).filter(h => h);
                const rowsArray = rows.trim().split('\n').map(row =>
                    row.split('|').map(cell => cell.trim()).filter(cell => cell)
                );

                let table = '<table class="params-table"><thead><tr>';
                headers.forEach(h => table += `<th>${h}</th>`);
                table += '</tr></thead><tbody>';

                rowsArray.forEach(row => {
                    table += '<tr>';
                    row.forEach(cell => table += `<td>${cell}</td>`);
                    table += '</tr>';
                });

                table += '</tbody></table>';
                return table;
            })
            // 链接
            .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>')
            // 粗体
            .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
            // 斜体
            .replace(/\*([^*]+)\*/g, '<em>$1</em>')
            // 换行
            .replace(/\n/g, '<br>');

        return html;
    }

    // 解析Word文档内容
    function parseWordApiDoc(text, filename) {
        const sections = [];
        const lines = text.split('\n').map(line => line.trim()).filter(line => line);

        let currentSection = null;
        let currentContent = '';
        let inCodeBlock = false;
        let codeContent = '';

        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 检测API接口标题（包含HTTP方法的行）
            const apiMethodMatch = line.match(/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+(.+)/i);
            if (apiMethodMatch) {
                // 保存上一个section
                if (currentSection) {
                    sections.push({
                        title: currentSection.title,
                        subtitle: currentSection.subtitle || 'API接口',
                        description: currentSection.description || '从Word文档解析的接口',
                        content: processWordContent(currentContent)
                    });
                }

                // 开始新的section
                const method = apiMethodMatch[1].toUpperCase();
                const path = apiMethodMatch[2].trim();
                currentSection = {
                    title: `${method} ${path}`,
                    subtitle: 'API接口',
                    description: '从Word文档解析的接口'
                };
                currentContent = '';
                continue;
            }

            // 检测章节标题（全大写或包含特定关键词）
            if (line.length > 0 && (
                line === line.toUpperCase() ||
                line.includes('接口') ||
                line.includes('API') ||
                line.includes('文档') ||
                /^\d+\./.test(line) // 数字开头的标题
            )) {
                // 保存上一个section
                if (currentSection) {
                    sections.push({
                        title: currentSection.title,
                        subtitle: currentSection.subtitle || 'Word文档内容',
                        description: currentSection.description || '从Word文档解析的内容',
                        content: processWordContent(currentContent)
                    });
                }

                // 开始新的section
                currentSection = {
                    title: line,
                    subtitle: 'Word文档内容',
                    description: '从Word文档解析的内容'
                };
                currentContent = '';
                continue;
            }

            // 检测代码块开始/结束
            if (line.includes('```') || line.includes('json') || line.includes('xml') || line.includes('{')) {
                if (!inCodeBlock) {
                    inCodeBlock = true;
                    codeContent = '';
                } else {
                    inCodeBlock = false;
                    currentContent += `\n<div class="code-example"><pre><code>${codeContent}</code></pre></div>\n`;
                    codeContent = '';
                }
                continue;
            }

            // 处理代码块内容
            if (inCodeBlock) {
                codeContent += line + '\n';
                continue;
            }

            // 普通内容
            currentContent += line + '\n';
        }

        // 保存最后一个section
        if (currentSection) {
            sections.push({
                title: currentSection.title,
                subtitle: currentSection.subtitle || 'Word文档内容',
                description: currentSection.description || '从Word文档解析的内容',
                content: processWordContent(currentContent)
            });
        }

        // 如果没有找到明确的sections，创建一个包含全部内容的section
        if (sections.length === 0) {
            sections.push({
                title: 'Word文档内容',
                subtitle: '文档内容',
                description: '从Word文档解析的完整内容',
                content: processWordContent(text)
            });
        }

        return {
            'uploaded-content': {
                title: filename.replace(/\.(doc|docx)$/i, '') || 'Word API文档',
                subtitle: 'Word格式文档',
                description: '从Word文档解析的API文档内容',
                sections: sections
            }
        };
    }

    // 处理Word文档内容，转换为HTML
    function processWordContent(content) {
        if (!content || content.trim() === '') {
            return '<p>暂无内容</p>';
        }

        let html = content
            // 检测表格格式（简单的列分隔）
            .replace(/^(.+?)\s+(.+?)\s+(.+?)$/gm, function(match, col1, col2, col3) {
                // 如果看起来像表格行，转换为表格
                if (col1 && col2 && col3 &&
                    (col1.includes('参数') || col1.includes('字段') || col1.includes('名称') ||
                     col2.includes('类型') || col2.includes('格式') ||
                     col3.includes('描述') || col3.includes('说明'))) {
                    return `<tr><td>${col1}</td><td><span class="type">${col2}</span></td><td>${col3}</td></tr>`;
                }
                return match;
            })
            // 检测JSON格式内容
            .replace(/\{[\s\S]*?\}/g, function(match) {
                try {
                    // 尝试格式化JSON
                    const parsed = JSON.parse(match);
                    return `<div class="code-example"><pre><code>${JSON.stringify(parsed, null, 2)}</code></pre></div>`;
                } catch (e) {
                    return `<div class="code-example"><pre><code>${match}</code></pre></div>`;
                }
            })
            // 检测URL路径
            .replace(/\/[a-zA-Z0-9\/_\-\{\}]+/g, function(match) {
                return `<span class="type">${match}</span>`;
            })
            // 检测HTTP状态码
            .replace(/\b(200|201|400|401|403|404|422|500)\b/g, function(match) {
                return `<span class="type">${match}</span>`;
            })
            // 检测常见的API术语
            .replace(/\b(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\b/g, function(match) {
                return `<span class="type">${match}</span>`;
            })
            // 转换换行为HTML
            .replace(/\n\n/g, '</p><p>')
            .replace(/\n/g, '<br>');

        // 如果包含表格行，包装在表格中
        if (html.includes('<tr>')) {
            html = `<table class="params-table"><thead><tr><th>参数名</th><th>类型</th><th>描述</th></tr></thead><tbody>${html}</tbody></table>`;
        } else {
            html = `<p>${html}</p>`;
        }

        return html;
    }

    // 更新API内容从文件
    function updateApiContentFromFile(newContent) {
        // 合并新内容到现有的apiContent
        Object.assign(apiContent, newContent);

        // 更新侧边栏
        updateSidebarWithNewContent(newContent);

        // 如果有上传的内容，自动切换到第一个
        const firstKey = Object.keys(newContent)[0];
        if (firstKey && newContent[firstKey]) {
            // 更新主要内容
            updateMainContent(newContent[firstKey]);
            // 更新右侧边栏
            updateRightSidebar(newContent[firstKey]);

            // 更新侧边栏活动状态
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.classList.remove('active');
                if (item.getAttribute('data-target') === firstKey) {
                    item.classList.add('active');
                }
            });
        }
    }

    // 更新侧边栏添加新内容
    function updateSidebarWithNewContent(newContent) {
        const sidebar = document.querySelector('.sidebar .sidebar-section');

        Object.keys(newContent).forEach(key => {
            const content = newContent[key];

            // 检查是否已存在
            const existing = sidebar.querySelector(`[data-target="${key}"]`);
            if (!existing) {
                const newItem = document.createElement('div');
                newItem.className = 'sidebar-item';
                newItem.setAttribute('data-target', key);
                newItem.textContent = content.title;

                // 添加点击事件
                newItem.addEventListener('click', function() {
                    const sidebarItems = document.querySelectorAll('.sidebar-item');
                    sidebarItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');

                    const targetContent = apiContent[key];
                    if (targetContent) {
                        updateMainContent(targetContent);
                        updateRightSidebar(targetContent);
                    }
                });

                // 插入到顶部（上传的内容优先显示）
                sidebar.insertBefore(newItem, sidebar.firstChild);
            }
        });
    }

    // 主题切换功能（可选）
    function addThemeToggle() {
        const themeToggle = document.createElement('button');
        themeToggle.textContent = '🌙';
        themeToggle.className = 'theme-toggle';
        themeToggle.style.cssText = `
            position: fixed;
            top: 60px;
            right: 20px;
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background-color: #007bff;
            color: white;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
        `;
        
        document.body.appendChild(themeToggle);
        
        themeToggle.addEventListener('click', function() {
            document.body.classList.toggle('dark-theme');
            this.textContent = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        });
    }
    
    // 添加主题切换按钮
    addThemeToggle();
    
    // 添加暗色主题样式
    const darkThemeStyles = `
        .dark-theme {
            background-color: #1a1a1a;
            color: #e0e0e0;
        }
        
        .dark-theme .container {
            background-color: #2d2d2d;
        }
        
        .dark-theme .sidebar,
        .dark-theme .right-sidebar {
            background-color: #252525;
            border-color: #404040;
        }
        
        .dark-theme .sidebar-item {
            color: #b0b0b0;
        }
        
        .dark-theme .sidebar-item:hover {
            background-color: #404040;
            color: #64b5f6;
        }
        
        .dark-theme .sidebar-item.active {
            background-color: #1e3a8a;
            color: #93c5fd;
        }
        
        .dark-theme .params-table {
            background-color: #2d2d2d;
            border-color: #404040;
        }
        
        .dark-theme .params-table th {
            background-color: #404040;
            color: #e0e0e0;
        }
        
        .dark-theme .params-table td {
            border-color: #404040;
        }
        
        .dark-theme .section-title {
            background-color: #404040;
            color: #e0e0e0;
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = darkThemeStyles;
    document.head.appendChild(styleSheet);
    
    console.log('API 文档页面已加载完成');
});
