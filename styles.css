/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

/* 顶部导航栏样式 */
.top-nav {
    background-color: #2c3e50;
    color: white;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 50px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 30px;
}

.logo {
    font-weight: bold;
    font-size: 16px;
}

.main-nav {
    display: flex;
    gap: 20px;
}

.nav-item {
    color: white;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.nav-item:hover {
    background-color: rgba(255,255,255,0.1);
}

.nav-right {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-size: 14px;
}

.nav-link.register {
    background-color: #3498db;
    padding: 6px 12px;
    border-radius: 4px;
}

/* 主容器布局 */
.container {
    display: flex;
    max-width: 1400px;
    margin: 0 auto;
    background-color: white;
    min-height: calc(100vh - 50px);
}

/* 左侧边栏样式 */
.sidebar {
    width: 200px;
    background-color: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-item {
    padding: 8px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
    border-left: 3px solid transparent;
    transition: all 0.3s;
}

.sidebar-item:hover {
    background-color: #e9ecef;
    color: #007bff;
}

.sidebar-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
    border-left-color: #1976d2;
    font-weight: 500;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 30px 40px;
    overflow-y: auto;
}

.breadcrumb {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 20px;
}

.breadcrumb-item {
    color: #007bff;
}

.page-title {
    font-size: 32px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.page-subtitle {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 5px;
}

.page-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 30px;
}

/* API 部分样式 */
.api-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #e9ecef;
}

.api-section:last-child {
    border-bottom: none;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-family: 'Courier New', monospace;
    background-color: #f8f9fa;
    padding: 10px 15px;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.section-subtitle {
    font-size: 16px;
    color: #6c757d;
    margin-bottom: 15px;
}

/* 参数表格样式 */
.params-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
    font-size: 14px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
}

.params-table th {
    background-color: #f8f9fa;
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.params-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.params-table tr:last-child td {
    border-bottom: none;
}

.params-table tr:hover {
    background-color: #f8f9fa;
}

.type {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    font-weight: 500;
}

.method-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.method-returns {
    color: #495057;
    font-size: 14px;
    margin-top: 10px;
}

/* 右侧边栏样式 */
.right-sidebar {
    width: 250px;
    background-color: #f8f9fa;
    border-left: 1px solid #e9ecef;
    padding: 30px 20px;
    overflow-y: auto;
}

.related-links h3 {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #007bff;
}

.related-links ul {
    list-style: none;
}

.related-links li {
    margin-bottom: 8px;
}

.related-links .link {
    color: #007bff;
    text-decoration: none;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    display: block;
    transition: all 0.3s;
}

.related-links .link:hover {
    background-color: #e3f2fd;
    color: #1565c0;
}

/* 代码示例样式 */
.code-example {
    margin: 20px 0;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;
}

.code-example pre {
    margin: 0;
    padding: 20px;
    background-color: #2d3748;
    color: #e2e8f0;
    overflow-x: auto;
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-example code {
    color: inherit;
    background: none;
    padding: 0;
    font-size: inherit;
}

/* 警告框样式 */
.warning-box {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-left: 4px solid #f39c12;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 4px;
    color: #856404;
}

.warning-box strong {
    color: #d68910;
}

/* 导航栏活动状态 */
.nav-item.active {
    background-color: #007bff;
    color: white;
}

/* 小标题样式 */
.api-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    margin: 25px 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

/* 文件上传样式 */
.upload-btn {
    background-color: #28a745;
    color: white !important;
    padding: 6px 12px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s;
}

.upload-btn:hover {
    background-color: #218838;
}

.upload-area {
    margin: 30px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}

.upload-zone {
    padding: 40px 20px;
    cursor: pointer;
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.6;
}

.upload-zone h3 {
    color: #495057;
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.upload-zone p {
    color: #6c757d;
    margin-bottom: 20px;
    font-size: 14px;
}

.select-file-btn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.select-file-btn:hover {
    background-color: #0056b3;
}

.upload-status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    font-size: 14px;
    display: none;
}

.upload-status.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.upload-status.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.upload-status.loading {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .container {
        max-width: 100%;
    }

    .right-sidebar {
        width: 200px;
    }
}

@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }

    .sidebar, .right-sidebar {
        width: 100%;
        max-height: 200px;
    }

    .main-content {
        padding: 20px;
    }

    .nav-left {
        gap: 15px;
    }

    .main-nav {
        gap: 10px;
    }

    .nav-item {
        padding: 6px 8px;
        font-size: 13px;
    }

    .code-example pre {
        padding: 15px;
        font-size: 12px;
    }
}
