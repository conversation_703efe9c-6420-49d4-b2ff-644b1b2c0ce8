用户管理API文档

这是一个用户管理系统的API接口文档，提供用户注册、登录、信息管理等功能。

1. 用户注册接口

POST /api/users/register

创建新的用户账户

请求参数:
username        string          用户名，3-20个字符
email           string          邮箱地址
password        string          密码，至少8个字符
phone           string          手机号码（可选）

请求示例:
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+86 138****8888"
}

响应示例:
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "created_at": "2024-01-15T10:30:00Z"
  }
}

2. 用户登录接口

POST /api/users/login

用户身份验证和获取访问令牌

请求参数:
username        string          用户名或邮箱
password        string          用户密码
remember_me     boolean         是否记住登录状态（可选）

请求示例:
{
  "username": "testuser",
  "password": "password123",
  "remember_me": true
}

响应示例:
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 3600,
    "user": {
      "id": 12345,
      "username": "testuser",
      "email": "<EMAIL>"
    }
  }
}

3. 获取用户信息

GET /api/users/{id}

根据用户ID获取用户详细信息

路径参数:
id              integer         用户ID

请求头:
Authorization   string          Bearer token

响应示例:
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "phone": "+86 138****8888",
    "avatar": "https://example.com/avatar/12345.jpg",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:45:00Z",
    "status": "active"
  }
}

4. 更新用户信息

PUT /api/users/{id}

更新指定用户的信息

路径参数:
id              integer         用户ID

请求参数:
email           string          新的邮箱地址（可选）
phone           string          新的手机号码（可选）
avatar          string          头像URL（可选）

请求示例:
{
  "email": "<EMAIL>",
  "phone": "+86 139****9999",
  "avatar": "https://example.com/avatar/new.jpg"
}

响应示例:
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 12345,
    "username": "testuser",
    "email": "<EMAIL>",
    "phone": "+86 139****9999",
    "avatar": "https://example.com/avatar/new.jpg",
    "updated_at": "2024-01-21T10:00:00Z"
  }
}

5. 删除用户

DELETE /api/users/{id}

删除指定的用户账户

路径参数:
id              integer         用户ID

请求头:
Authorization   string          Bearer token（需要管理员权限）

响应示例:
{
  "code": 200,
  "message": "用户已删除"
}

错误处理

API使用标准HTTP状态码：

200 - 请求成功
201 - 资源创建成功
400 - 请求参数错误
401 - 未授权访问
403 - 禁止访问
404 - 资源不存在
422 - 请求参数验证失败
500 - 服务器内部错误

错误响应格式:
{
  "code": 400,
  "message": "请求参数错误",
  "errors": {
    "username": ["用户名不能为空"],
    "email": ["邮箱格式不正确"]
  }
}

认证说明

除了注册和登录接口外，所有API接口都需要在请求头中包含认证信息：

Authorization: Bearer <access_token>

访问令牌通过登录接口获取，有效期为1小时。

速率限制

为了保护系统稳定性，API实施以下速率限制：

- 普通用户：每分钟60次请求
- VIP用户：每分钟300次请求
- 管理员：每分钟600次请求

超出限制时将返回429状态码。
