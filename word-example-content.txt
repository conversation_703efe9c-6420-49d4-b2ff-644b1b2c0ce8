订单管理系统API文档

本文档描述了订单管理系统的REST API接口，包括订单创建、查询、更新和删除等功能。

1. 订单管理接口

1.1 创建订单

POST /api/orders

创建新的订单记录

请求参数：
参数名          类型            描述
customer_id     integer         客户ID
product_id      integer         产品ID
quantity        integer         订购数量
price           decimal         单价
total_amount    decimal         总金额
shipping_address string         收货地址
payment_method  string          支付方式

请求示例：
{
  "customer_id": 12345,
  "product_id": 67890,
  "quantity": 2,
  "price": 299.99,
  "total_amount": 599.98,
  "shipping_address": "北京市朝阳区xxx街道xxx号",
  "payment_method": "credit_card"
}

响应示例：
{
  "code": 201,
  "message": "订单创建成功",
  "data": {
    "order_id": "ORD20240115001",
    "customer_id": 12345,
    "product_id": 67890,
    "quantity": 2,
    "price": 299.99,
    "total_amount": 599.98,
    "status": "pending",
    "created_at": "2024-01-15T10:30:00Z"
  }
}

1.2 查询订单

GET /api/orders/{order_id}

根据订单ID查询订单详情

路径参数：
参数名          类型            描述
order_id        string          订单ID

响应示例：
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "order_id": "ORD20240115001",
    "customer_id": 12345,
    "customer_name": "张三",
    "product_id": 67890,
    "product_name": "iPhone 15 Pro",
    "quantity": 2,
    "price": 299.99,
    "total_amount": 599.98,
    "status": "shipped",
    "shipping_address": "北京市朝阳区xxx街道xxx号",
    "payment_method": "credit_card",
    "payment_status": "paid",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-16T14:20:00Z"
  }
}

1.3 更新订单状态

PUT /api/orders/{order_id}/status

更新订单的状态信息

路径参数：
参数名          类型            描述
order_id        string          订单ID

请求参数：
参数名          类型            描述
status          string          新的订单状态
notes           string          备注信息

状态说明：
- pending: 待处理
- confirmed: 已确认
- processing: 处理中
- shipped: 已发货
- delivered: 已送达
- cancelled: 已取消

请求示例：
{
  "status": "shipped",
  "notes": "订单已发货，快递单号：SF1234567890"
}

1.4 获取订单列表

GET /api/orders

获取订单列表，支持分页和筛选

查询参数：
参数名          类型            描述
page            integer         页码，默认为1
limit           integer         每页数量，默认为20
status          string          订单状态筛选
customer_id     integer         客户ID筛选
start_date      string          开始日期
end_date        string          结束日期

2. 客户管理接口

2.1 获取客户信息

GET /api/customers/{customer_id}

根据客户ID获取客户详细信息

路径参数：
参数名          类型            描述
customer_id     integer         客户ID

响应示例：
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "customer_id": 12345,
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "+86 138****8888",
    "address": "北京市朝阳区xxx街道xxx号",
    "registration_date": "2023-06-15T09:00:00Z",
    "total_orders": 15,
    "total_amount": 8999.85,
    "status": "active"
  }
}

3. 产品管理接口

3.1 获取产品信息

GET /api/products/{product_id}

根据产品ID获取产品详细信息

路径参数：
参数名          类型            描述
product_id      integer         产品ID

响应示例：
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "product_id": 67890,
    "name": "iPhone 15 Pro",
    "description": "最新款iPhone手机，配备A17 Pro芯片",
    "price": 7999.00,
    "category": "手机",
    "brand": "Apple",
    "stock": 150,
    "images": [
      "https://example.com/images/iphone15pro-1.jpg",
      "https://example.com/images/iphone15pro-2.jpg"
    ],
    "specifications": {
      "screen_size": "6.1英寸",
      "storage": "128GB",
      "color": "深空黑色",
      "weight": "187g"
    },
    "status": "active"
  }
}

4. 错误处理

API使用标准HTTP状态码：

200 - 请求成功
201 - 资源创建成功
400 - 请求参数错误
401 - 未授权访问
403 - 禁止访问
404 - 资源不存在
422 - 请求参数验证失败
500 - 服务器内部错误

错误响应格式：
{
  "code": 400,
  "message": "请求参数错误",
  "errors": {
    "customer_id": ["客户ID不能为空"],
    "quantity": ["数量必须大于0"]
  }
}

5. 认证说明

所有API接口都需要在请求头中包含认证信息：

Authorization: Bearer <access_token>

获取访问令牌请联系系统管理员或使用OAuth2.0认证流程。

6. 速率限制

为了保护系统稳定性，API实施以下速率限制：

- 普通用户：每分钟100次请求
- VIP用户：每分钟500次请求
- 系统集成：每分钟1000次请求

超出限制时将返回429状态码。
